#!/usr/bin/env python3
"""
测试重新加载文档时是否能正确获取和设置redirect_url和content_url
"""

import sys
import os
import requests
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService

def test_data_pipeline_connection():
    """测试data_pipeline API连接"""
    print("🔍 测试data_pipeline API连接...")
    
    try:
        response = requests.get("http://localhost:8001/articles/summary", timeout=10)
        response.raise_for_status()
        articles = response.json()
        
        print(f"✅ data_pipeline API连接成功")
        print(f"📊 获取到 {len(articles)} 个文章")
        
        # 显示前几个文章的信息
        for i, article in enumerate(articles[:3]):
            print(f"   文章 {i+1}: {article.get('title', 'N/A')}")
            print(f"     redirect_url: {article.get('redirect_url', 'None')}")
            print(f"     content_url: {article.get('content_url', 'None')}")
        
        return True
        
    except Exception as e:
        print(f"❌ data_pipeline API连接失败: {e}")
        return False

def test_load_documents_with_urls():
    """测试重新加载文档时获取URL信息"""
    print("\n🔄 测试重新加载文档...")
    
    try:
        # 初始化 RAG 服务
        rag_service = RAGService()
        print("✅ RAG 服务初始化成功")
        
        # 重新加载文档
        result = rag_service.load_documents()
        
        if not result["success"]:
            print(f"❌ 重新加载文档失败: {result['message']}")
            return False
        
        print(f"✅ 重新加载文档成功")
        print(f"📊 处理文档数量: {result['documents_processed']}")
        print(f"📊 总文档块数: {result['total_chunks']}")
        print(f"📊 找到URL信息的文档数: {result.get('url_info_found', 0)}")
        
        # 验证元数据是否包含URL信息
        print("\n🔍 验证文档元数据...")
        
        collection = rag_service.collection
        if not collection:
            print("❌ 集合未初始化")
            return False
        
        # 获取所有文档的元数据
        result = collection.get(include=["metadatas"])
        
        if not result["ids"]:
            print("❌ 未找到任何文档")
            return False
        
        print(f"📊 找到 {len(result['ids'])} 个文档块")
        
        # 统计包含URL信息的文档块
        redirect_url_count = 0
        content_url_count = 0
        
        for i, metadata in enumerate(result["metadatas"]):
            if metadata and metadata.get("redirect_url"):
                redirect_url_count += 1
            if metadata and metadata.get("content_url"):
                content_url_count += 1
        
        print(f"📊 包含redirect_url的文档块: {redirect_url_count}")
        print(f"📊 包含content_url的文档块: {content_url_count}")
        
        # 显示前几个文档的URL信息
        print("\n📋 前几个文档的URL信息:")
        shown_files = set()
        for i, metadata in enumerate(result["metadatas"][:10]):
            if metadata:
                filename = metadata.get("filename", "未知文件")
                if filename not in shown_files:
                    shown_files.add(filename)
                    redirect_url = metadata.get("redirect_url", "None")
                    content_url = metadata.get("content_url", "None")
                    print(f"   文件: {filename}")
                    print(f"     redirect_url: {redirect_url}")
                    print(f"     content_url: {content_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试重新加载文档时的URL获取功能")
    print("=" * 60)
    
    # 测试data_pipeline API连接
    if not test_data_pipeline_connection():
        print("\n❌ data_pipeline API连接失败，请确保服务已启动")
        return False
    
    # 测试重新加载文档
    if not test_load_documents_with_urls():
        print("\n❌ 重新加载文档测试失败")
        return False
    
    print("\n✅ 所有测试通过！")
    print("🎉 重新加载文档时能够正确获取和设置URL信息")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
