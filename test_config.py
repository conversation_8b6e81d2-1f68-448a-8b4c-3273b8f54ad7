#!/usr/bin/env python3
"""
配置和数据库状态检查脚本
"""
import os
import sys
import chromadb
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config():
    """测试配置"""
    print("=== 配置检查 ===")
    
    try:
        from backend.config.settings import settings
        print(f"✅ 配置加载成功")
        print(f"数据目录: {settings.data_dir}")
        print(f"存储目录: {settings.storage_dir}")
        print(f"Chroma持久化目录: {settings.chroma_persist_directory}")
        print(f"集合名称: {settings.collection_name}")
        print(f"OpenAI API Key: {settings.openai_api_key[:10]}...")
        print(f"OpenAI Base URL: {settings.openai_base_url}")
        
        # 检查目录是否存在
        print(f"\n=== 目录检查 ===")
        print(f"数据目录存在: {os.path.exists(settings.data_dir)}")
        print(f"存储目录存在: {os.path.exists(settings.storage_dir)}")
        print(f"Chroma目录存在: {os.path.exists(settings.chroma_persist_directory)}")
        
        # 检查数据目录中的文件
        if os.path.exists(settings.data_dir):
            txt_files = list(Path(settings.data_dir).glob("*.txt"))
            print(f"数据目录中的TXT文件数量: {len(txt_files)}")
            for file in txt_files:
                print(f"  - {file.name} ({file.stat().st_size} bytes)")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    return True

def test_chromadb():
    """测试ChromaDB连接"""
    print(f"\n=== ChromaDB检查 ===")
    
    try:
        from backend.config.settings import settings
        
        # 创建ChromaDB客户端
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        print(f"✅ ChromaDB客户端创建成功")
        
        # 获取集合列表
        collections = client.list_collections()
        print(f"集合列表: {[col.name for col in collections]}")
        
        # 检查documents集合
        try:
            collection = client.get_collection(name=settings.collection_name)
            print(f"✅ 找到集合: {settings.collection_name}")
            
            # 获取集合信息
            count = collection.count()
            print(f"集合中的文档数量: {count}")
            
            if count > 0:
                # 获取一些示例数据
                result = collection.get(limit=5, include=["metadatas"])
                print(f"示例文档元数据:")
                for i, metadata in enumerate(result["metadatas"][:3]):
                    print(f"  {i+1}. {metadata}")
            
        except Exception as e:
            print(f"❌ 获取集合失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ ChromaDB连接失败: {e}")
        return False
    
    return True

def test_rag_service():
    """测试RAG服务"""
    print(f"\n=== RAG服务检查 ===")
    
    try:
        from backend.app.rag_service import RAGService
        
        # 创建RAG服务实例
        rag_service = RAGService()
        print(f"✅ RAG服务初始化成功")
        
        # 获取状态
        status = rag_service.get_status()
        print(f"系统状态: {status}")
        
        # 获取文档列表
        docs_result = rag_service.get_documents_list()
        print(f"文档列表结果: {docs_result}")
        
        if docs_result["success"]:
            print(f"文档数量: {len(docs_result['documents'])}")
            print(f"总块数: {docs_result['total_chunks']}")
            
            for doc in docs_result["documents"]:
                print(f"  - {doc['filename']}: {doc['chunks_count']} 块")
        
    except Exception as e:
        print(f"❌ RAG服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("开始配置和数据库状态检查...\n")
    
    # 测试配置
    config_ok = test_config()
    
    # 测试ChromaDB
    chroma_ok = test_chromadb()
    
    # 测试RAG服务
    rag_ok = test_rag_service()
    
    print(f"\n=== 检查结果 ===")
    print(f"配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"ChromaDB检查: {'✅ 通过' if chroma_ok else '❌ 失败'}")
    print(f"RAG服务检查: {'✅ 通过' if rag_ok else '❌ 失败'}")
    
    if all([config_ok, chroma_ok, rag_ok]):
        print(f"\n🎉 所有检查都通过了！")
    else:
        print(f"\n⚠️ 存在一些问题，请检查上述错误信息")

if __name__ == "__main__":
    main() 