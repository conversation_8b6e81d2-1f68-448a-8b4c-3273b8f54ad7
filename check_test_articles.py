#!/usr/bin/env python3
"""
检查测试文章的详细信息
"""

import requests
import json

def check_test_articles():
    """检查测试文章"""
    print("🔍 检查测试文章...")
    
    # 测试文章的ID列表
    test_ids = [
        "685650404978757",
        "685650870968389", 
        "685650916724805",
        "685650973442117"
    ]
    
    print(f"📊 发现 {len(test_ids)} 个'测试测试'文章")
    
    for i, article_id in enumerate(test_ids, 1):
        print(f"\n📝 测试文章 {i} (ID: {article_id}):")
        
        # 尝试获取文章内容
        try:
            response = requests.get(f"http://localhost:8001/articles/{article_id}/txt")
            print(f"   📄 内容获取状态: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"   📊 内容长度: {len(content)} 字符")
                print(f"   📄 内容预览: {content[:100]}...")
            else:
                error_detail = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                print(f"   ❌ 错误: {error_detail}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def check_contact_article():
    """检查联系方式文章"""
    print(f"\n🔍 检查'贵阳人文科技学院党政办公室联系方式'文章...")
    
    article_id = "310000000002751"
    
    try:
        response = requests.get(f"http://localhost:8001/articles/{article_id}/txt")
        print(f"   📄 内容获取状态: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"   📊 内容长度: {len(content)} 字符")
            print(f"   📄 内容预览: {content[:200]}...")
        else:
            error_detail = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            print(f"   ❌ 错误: {error_detail}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")

def analyze_sync_logic():
    """分析同步逻辑"""
    print(f"\n🔍 分析同步逻辑...")
    
    try:
        # 获取 to-add 数据
        response = requests.get("http://localhost:8001/articles/to-add")
        if response.status_code == 200:
            to_add_data = response.json()
            
            # 按文件名分组
            filename_groups = {}
            for article in to_add_data:
                filename = article.get('filename', 'unknown')
                if filename not in filename_groups:
                    filename_groups[filename] = []
                filename_groups[filename].append(article)
            
            print(f"📊 to-add 接口返回 {len(to_add_data)} 个文章")
            print(f"📊 按文件名分组后有 {len(filename_groups)} 个不同的文件名")
            
            for filename, articles in filename_groups.items():
                print(f"\n📁 文件名: {filename}")
                print(f"   📊 包含 {len(articles)} 个文章:")
                for article in articles:
                    print(f"     - ID: {article.get('content_id')}, 标题: {article.get('title')}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🚀 检查测试文章详情")
    print("=" * 60)
    
    # 检查测试文章
    check_test_articles()
    
    # 检查联系方式文章
    check_contact_article()
    
    # 分析同步逻辑
    analyze_sync_logic()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    print("1. 多个'测试测试'文章都没有内容，无法同步")
    print("2. '联系方式'文章也没有内容")
    print("3. 这些文章在CMS中存在但无法生成有效的txt文件")
    print("4. 所以虽然显示'新增 2 个'，但实际上无法成功添加")
    print("5. 建议清理这些无内容的测试文章，或者为它们添加内容")

if __name__ == "__main__":
    main()
