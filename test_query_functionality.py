#!/usr/bin/env python3
"""
测试查询功能是否正常工作
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService

def test_queries():
    """测试多个查询"""
    print("🔍 测试查询功能...")
    
    test_queries = [
        "介绍一下新闻学",
        "学校有哪些专业",
        "联系方式",
        "贵阳人文科技学院",
        "党政办公室",
        "科研处",
        "体育与艺术学院"
    ]
    
    try:
        rag_service = RAGService()
        print(f"📊 ChromaDB 文档数: {rag_service.collection.count()}")
        
        success_count = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 测试查询 {i}: {query}")
            
            try:
                result = rag_service.query(query, max_results=3)
                
                if result["success"]:
                    print(f"✅ 查询成功")
                    print(f"📊 答案长度: {len(result['answer'])}")
                    print(f"📊 源文档数量: {len(result['sources'])}")
                    print(f"📄 答案预览: {result['answer'][:150]}...")
                    
                    if result['sources']:
                        print(f"📋 源文档:")
                        for j, source in enumerate(result['sources'][:2]):
                            print(f"   {j+1}. {source['filename']}")
                            print(f"      内容: {source['content'][:100]}...")
                    
                    success_count += 1
                else:
                    print(f"❌ 查询失败: {result['message']}")
                    
            except Exception as e:
                print(f"❌ 查询异常: {e}")
        
        print(f"\n📊 总结: {success_count}/{len(test_queries)} 个查询成功")
        return success_count == len(test_queries)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_simulation():
    """模拟 API 调用"""
    print("\n🔍 模拟 API 调用...")
    
    try:
        rag_service = RAGService()
        
        # 模拟 main.py 中的查询逻辑
        query_data = {
            "query": "介绍一下新闻学",
            "max_results": 5,
            "similarity_threshold": 0.7
        }
        
        print(f"📝 模拟查询: {query_data}")
        
        # 调用查询方法
        result = rag_service.query(
            question=query_data["query"],
            max_results=query_data["max_results"]
        )
        
        if result["success"]:
            print(f"✅ API 模拟成功")
            print(f"📊 答案: {result['answer'][:200]}...")
            print(f"📊 源文档数量: {len(result['sources'])}")
            return True
        else:
            print(f"❌ API 模拟失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ API 模拟异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试查询功能")
    print("=" * 60)
    
    # 测试查询
    if not test_queries():
        print("❌ 查询测试失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试 API 模拟
    if not test_api_simulation():
        print("❌ API 模拟测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！")
    print("🎉 查询功能正常工作")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
