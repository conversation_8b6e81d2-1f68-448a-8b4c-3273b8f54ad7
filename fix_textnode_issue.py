#!/usr/bin/env python3
"""
修复 TextNode 验证错误
清理并重建索引
"""

import sys
import os
import shutil
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService
from backend.config.settings import settings

def backup_data():
    """备份当前数据"""
    print("💾 备份当前数据...")
    
    try:
        storage_path = Path(settings.chroma_persist_directory)
        if storage_path.exists():
            backup_path = storage_path.parent / f"{storage_path.name}_backup"
            if backup_path.exists():
                shutil.rmtree(backup_path)
            shutil.copytree(storage_path, backup_path)
            print(f"✅ 数据已备份到: {backup_path}")
        else:
            print("⚠️  存储目录不存在，无需备份")
        
        return True
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def clean_storage():
    """清理存储目录"""
    print("🗑️  清理存储目录...")
    
    try:
        storage_path = Path(settings.chroma_persist_directory)
        if storage_path.exists():
            shutil.rmtree(storage_path)
            print(f"✅ 已删除存储目录: {storage_path}")
        
        # 重新创建目录
        storage_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 已重新创建存储目录: {storage_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理存储目录失败: {e}")
        return False

def rebuild_index():
    """重建索引"""
    print("🔧 重建索引...")
    
    try:
        # 创建新的 RAG 服务实例
        rag_service = RAGService()
        print("✅ RAG 服务初始化成功")
        
        # 重新加载文档
        print("📁 重新加载文档...")
        result = rag_service.load_documents()
        
        if result["success"]:
            print(f"✅ 文档加载成功")
            print(f"📊 处理文档数量: {result['documents_processed']}")
            print(f"📊 总文档块数: {result['total_chunks']}")
            print(f"📊 找到URL信息的文档数: {result.get('url_info_found', 0)}")
        else:
            print(f"❌ 文档加载失败: {result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 重建索引失败: {e}")
        return False

def test_query():
    """测试查询功能"""
    print("🔍 测试查询功能...")
    
    try:
        rag_service = RAGService()
        
        # 测试简单查询
        test_queries = [
            "介绍一下新闻学",
            "学校有哪些专业",
            "联系方式"
        ]
        
        for query in test_queries:
            print(f"\n📝 测试查询: {query}")
            result = rag_service.query(query, max_results=3)
            
            if result["success"]:
                print(f"✅ 查询成功")
                print(f"📊 答案长度: {len(result['answer'])}")
                print(f"📊 源文档数量: {len(result['sources'])}")
                print(f"📄 答案预览: {result['answer'][:100]}...")
            else:
                print(f"❌ 查询失败: {result['message']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试查询失败: {e}")
        return False

def check_data_integrity():
    """检查数据完整性"""
    print("🔍 检查数据完整性...")
    
    try:
        rag_service = RAGService()
        
        # 检查集合
        collection = rag_service.collection
        if not collection:
            print("❌ 集合未初始化")
            return False
        
        collection_count = collection.count()
        print(f"📊 ChromaDB 集合文档数: {collection_count}")
        
        # 检查索引
        if not rag_service.index:
            print("❌ 索引未初始化")
            return False
        
        # 检查 docstore
        docstore = rag_service.index.docstore
        docstore_count = len(docstore.docs)
        print(f"📊 索引 docstore 文档数: {docstore_count}")
        
        # 检查是否同步
        if collection_count == docstore_count:
            print("✅ ChromaDB 和索引数据同步")
        else:
            print(f"⚠️  数据不同步: ChromaDB={collection_count}, docstore={docstore_count}")
        
        # 检查文档内容
        result = collection.get(include=["documents"], limit=5)
        none_count = 0
        empty_count = 0
        
        for doc in result.get("documents", []):
            if doc is None:
                none_count += 1
            elif doc == "" or len(doc.strip()) == 0:
                empty_count += 1
        
        print(f"📊 None 文档数: {none_count}")
        print(f"📊 空文档数: {empty_count}")
        
        return none_count == 0 and collection_count == docstore_count
        
    except Exception as e:
        print(f"❌ 检查数据完整性失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复 TextNode 验证错误")
    print("=" * 60)
    
    # 备份数据
    if not backup_data():
        print("❌ 备份失败，停止操作")
        return False
    
    print("\n" + "=" * 60)
    
    # 清理存储
    if not clean_storage():
        print("❌ 清理存储失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 重建索引
    if not rebuild_index():
        print("❌ 重建索引失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 检查数据完整性
    if not check_data_integrity():
        print("❌ 数据完整性检查失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试查询
    if not test_query():
        print("❌ 查询测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 所有修复步骤完成！")
    print("🎉 TextNode 验证错误已修复，查询功能正常")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
