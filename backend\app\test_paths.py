#!/usr/bin/env python3
"""
路径配置测试脚本
验证配置文件中的路径是否正确解析
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def test_path_configuration():
    """测试路径配置"""
    print("🔍 测试路径配置")
    print("=" * 50)
    
    try:
        # 导入配置
        from backend.config.settings import settings
        
        print("✅ 配置加载成功")
        print(f"   项目根目录: {project_root}")
        print(f"   数据目录: {settings.data_dir}")
        print(f"   存储目录: {settings.storage_dir}")
        print(f"   ChromaDB目录: {settings.chroma_persist_directory}")
        
        # 检查目录是否存在
        print("\n📁 目录存在性检查:")
        
        data_dir = Path(settings.data_dir)
        storage_dir = Path(settings.storage_dir)
        chroma_dir = Path(settings.chroma_persist_directory)
        
        print(f"   数据目录存在: {data_dir.exists()} - {data_dir}")
        print(f"   存储目录存在: {storage_dir.exists()} - {storage_dir}")
        print(f"   ChromaDB目录存在: {chroma_dir.exists()} - {chroma_dir}")
        
        # 检查ChromaDB文件
        chroma_db_file = chroma_dir / "chroma.sqlite3"
        print(f"   ChromaDB文件存在: {chroma_db_file.exists()} - {chroma_db_file}")
        
        # 检查路径是否为绝对路径
        print("\n🔗 路径类型检查:")
        print(f"   数据目录是绝对路径: {os.path.isabs(settings.data_dir)}")
        print(f"   存储目录是绝对路径: {os.path.isabs(settings.storage_dir)}")
        print(f"   ChromaDB目录是绝对路径: {os.path.isabs(settings.chroma_persist_directory)}")
        
        # 检查路径是否在项目根目录下
        print("\n📍 路径位置检查:")
        print(f"   数据目录在项目根目录下: {data_dir.is_relative_to(project_root) if data_dir.exists() else 'N/A'}")
        print(f"   存储目录在项目根目录下: {storage_dir.is_relative_to(project_root) if storage_dir.exists() else 'N/A'}")
        print(f"   ChromaDB目录在项目根目录下: {chroma_dir.is_relative_to(project_root) if chroma_dir.exists() else 'N/A'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_env_file():
    """测试环境变量文件"""
    print("\n🔍 测试环境变量文件")
    print("=" * 50)
    
    env_file = Path(__file__).parent / ".env"
    print(f"   环境变量文件: {env_file}")
    print(f"   文件存在: {env_file.exists()}")
    
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键配置项
            lines = content.split('\n')
            for line in lines:
                if line.startswith('DATA_DIR=') or line.startswith('STORAGE_DIR=') or line.startswith('CHROMA_PERSIST_DIRECTORY='):
                    print(f"   {line.strip()}")
            
        except Exception as e:
            print(f"   ❌ 读取环境变量文件失败: {e}")

def main():
    """主函数"""
    print("🎯 路径配置测试")
    print("=" * 60)
    
    # 测试环境变量文件
    test_env_file()
    
    # 测试路径配置
    success = test_path_configuration()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if success:
        print("✅ 路径配置测试通过！")
        print("🚀 配置文件路径设置正确")
    else:
        print("❌ 路径配置测试失败")
        print("⚠️  请检查配置文件设置")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 