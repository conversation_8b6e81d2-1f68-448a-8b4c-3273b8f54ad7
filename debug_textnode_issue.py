#!/usr/bin/env python3
"""
调试 TextNode 验证错误
检查文档数据的完整性
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService

def debug_collection_data():
    """检查 ChromaDB 集合中的数据"""
    print("🔍 检查 ChromaDB 集合数据...")
    
    try:
        rag_service = RAGService()
        collection = rag_service.collection
        
        if not collection:
            print("❌ 集合未初始化")
            return False
        
        # 获取所有数据
        result = collection.get(include=["documents", "metadatas"])
        
        print(f"📊 集合中总文档块数: {len(result['ids'])}")
        
        # 检查空文档
        empty_docs = 0
        none_docs = 0
        
        for i, doc in enumerate(result.get("documents", [])):
            if doc is None:
                none_docs += 1
                print(f"❌ 发现 None 文档，ID: {result['ids'][i]}")
            elif doc == "":
                empty_docs += 1
                print(f"⚠️  发现空文档，ID: {result['ids'][i]}")
            elif len(doc.strip()) == 0:
                empty_docs += 1
                print(f"⚠️  发现空白文档，ID: {result['ids'][i]}")
        
        print(f"📊 None 文档数量: {none_docs}")
        print(f"📊 空文档数量: {empty_docs}")
        
        # 显示前几个文档的信息
        print("\n📋 前5个文档信息:")
        for i in range(min(5, len(result['ids']))):
            doc_id = result['ids'][i]
            doc_text = result.get("documents", [None])[i] if i < len(result.get("documents", [])) else None
            metadata = result.get("metadatas", [{}])[i] if i < len(result.get("metadatas", [])) else {}
            
            print(f"   ID: {doc_id}")
            print(f"   文件名: {metadata.get('filename', 'N/A')}")
            print(f"   文档长度: {len(doc_text) if doc_text else 'None'}")
            print(f"   文档预览: {(doc_text[:50] + '...') if doc_text and len(doc_text) > 50 else doc_text}")
            print()
        
        return none_docs == 0
        
    except Exception as e:
        print(f"❌ 检查集合数据失败: {e}")
        return False

def debug_index_nodes():
    """检查索引中的节点"""
    print("🔍 检查索引节点...")
    
    try:
        rag_service = RAGService()
        
        if not rag_service.index:
            print("❌ 索引未初始化")
            return False
        
        # 尝试获取所有节点
        try:
            # 通过 docstore 获取所有文档
            docstore = rag_service.index.docstore
            all_doc_ids = list(docstore.docs.keys())
            
            print(f"📊 索引中文档数量: {len(all_doc_ids)}")
            
            # 检查前几个文档
            none_text_count = 0
            empty_text_count = 0
            
            for i, doc_id in enumerate(all_doc_ids[:10]):
                doc = docstore.get_document(doc_id)
                if doc:
                    if doc.text is None:
                        none_text_count += 1
                        print(f"❌ 发现 None text 的文档，ID: {doc_id}")
                    elif doc.text == "":
                        empty_text_count += 1
                        print(f"⚠️  发现空 text 的文档，ID: {doc_id}")
                    else:
                        print(f"✅ 文档 {i+1}: ID={doc_id}, text长度={len(doc.text)}")
            
            print(f"📊 None text 文档数量: {none_text_count}")
            print(f"📊 空 text 文档数量: {empty_text_count}")
            
            return none_text_count == 0
            
        except Exception as e:
            print(f"❌ 检查索引节点失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 检查索引失败: {e}")
        return False

def test_simple_query():
    """测试简单查询"""
    print("🔍 测试简单查询...")
    
    try:
        rag_service = RAGService()
        
        # 尝试简单查询
        result = rag_service.query("测试", max_results=1)
        
        if result["success"]:
            print("✅ 查询成功")
            print(f"📊 答案: {result['answer'][:100]}...")
            print(f"📊 源文档数量: {len(result['sources'])}")
        else:
            print(f"❌ 查询失败: {result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试查询失败: {e}")
        return False

def clean_and_rebuild_index():
    """清理并重建索引"""
    print("🔧 清理并重建索引...")
    
    try:
        rag_service = RAGService()
        
        # 清空集合
        collection = rag_service.collection
        if collection:
            # 获取所有ID并删除
            result = collection.get()
            if result["ids"]:
                collection.delete(ids=result["ids"])
                print(f"🗑️  删除了 {len(result['ids'])} 个文档块")
        
        # 重新加载文档
        print("📁 重新加载文档...")
        load_result = rag_service.load_documents()
        
        if load_result["success"]:
            print(f"✅ 重新加载成功，处理了 {load_result['documents_processed']} 个文件")
            print(f"📊 创建了 {load_result['total_chunks']} 个文档块")
        else:
            print(f"❌ 重新加载失败: {load_result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 清理重建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始调试 TextNode 验证错误")
    print("=" * 60)
    
    # 检查集合数据
    collection_ok = debug_collection_data()
    
    print("\n" + "=" * 60)
    
    # 检查索引节点
    index_ok = debug_index_nodes()
    
    print("\n" + "=" * 60)
    
    # 如果数据有问题，尝试清理重建
    if not collection_ok or not index_ok:
        print("⚠️  发现数据问题，尝试清理重建...")
        if clean_and_rebuild_index():
            print("✅ 清理重建成功")
            
            # 重新测试查询
            print("\n" + "=" * 60)
            if test_simple_query():
                print("✅ 查询测试成功")
            else:
                print("❌ 查询测试仍然失败")
        else:
            print("❌ 清理重建失败")
    else:
        # 直接测试查询
        print("📊 数据看起来正常，测试查询...")
        if test_simple_query():
            print("✅ 查询测试成功")
        else:
            print("❌ 查询测试失败")

if __name__ == "__main__":
    main()
