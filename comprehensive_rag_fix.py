#!/usr/bin/env python3
"""
综合修复RAG查询问题
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService

def step1_analyze_data():
    """步骤1: 分析ChromaDB数据"""
    print("🔍 步骤1: 分析ChromaDB数据...")
    
    try:
        rag_service = RAGService()
        collection = rag_service.collection
        
        # 获取所有数据
        result = collection.get(include=["documents", "metadatas"])
        
        print(f"📊 总文档数量: {len(result['ids'])}")
        
        # 检查数据质量
        none_docs = 0
        empty_docs = 0
        valid_docs = 0
        
        for doc_text in result.get("documents", []):
            if doc_text is None:
                none_docs += 1
            elif isinstance(doc_text, str) and doc_text.strip() == "":
                empty_docs += 1
            else:
                valid_docs += 1
        
        print(f"📊 None文档: {none_docs}")
        print(f"📊 空文档: {empty_docs}")
        print(f"📊 有效文档: {valid_docs}")
        
        return {"total": len(result['ids']), "none": none_docs, "empty": empty_docs, "valid": valid_docs}
        
    except Exception as e:
        print(f"❌ 分析数据失败: {e}")
        return None

def step2_clean_data():
    """步骤2: 清理无效数据"""
    print("\n🔧 步骤2: 清理无效数据...")
    
    try:
        rag_service = RAGService()
        
        # 使用新的清理方法
        rag_service._clean_chroma_data()
        
        # 重新检查数据
        collection = rag_service.collection
        result = collection.get(include=["documents", "metadatas"])
        
        print(f"✅ 清理后文档数量: {len(result['ids'])}")
        return True
        
    except Exception as e:
        print(f"❌ 清理数据失败: {e}")
        return False

def step3_rebuild_index():
    """步骤3: 重建索引"""
    print("\n🔄 步骤3: 重建索引...")
    
    try:
        rag_service = RAGService()
        
        # 使用新的重建方法
        rag_service._rebuild_index_from_chroma()
        
        print("✅ 索引重建完成")
        return rag_service
        
    except Exception as e:
        print(f"❌ 重建索引失败: {e}")
        return None

def step4_test_query(rag_service):
    """步骤4: 测试查询"""
    print("\n🔍 步骤4: 测试查询...")
    
    try:
        # 测试简单查询
        result = rag_service.query("介绍", max_results=1)
        
        if result["success"]:
            print("✅ 查询成功")
            print(f"📝 答案: {result['answer'][:100]}...")
            print(f"📊 源文档数量: {len(result['sources'])}")
            return True
        else:
            print(f"❌ 查询失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试查询失败: {e}")
        return False

def step5_full_reload():
    """步骤5: 完全重新加载（如果前面步骤失败）"""
    print("\n🔄 步骤5: 完全重新加载文档...")
    
    try:
        rag_service = RAGService()
        
        # 清空所有数据
        collection = rag_service.collection
        all_data = collection.get()
        if all_data["ids"]:
            collection.delete(ids=all_data["ids"])
            print(f"✅ 清空了 {len(all_data['ids'])} 个文档块")
        
        # 重新加载文档
        result = rag_service.load_documents()
        
        if result["success"]:
            print(f"✅ 重新加载成功: {result['documents_processed']} 个文档")
            print(f"📊 总文档块数: {result['total_chunks']}")
            return rag_service
        else:
            print(f"❌ 重新加载失败: {result['message']}")
            return None
            
    except Exception as e:
        print(f"❌ 完全重新加载失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 综合修复RAG查询问题")
    print("=" * 60)
    
    # 步骤1: 分析数据
    data_info = step1_analyze_data()
    if not data_info:
        return False
    
    # 如果有无效数据，进行清理
    if data_info["none"] > 0 or data_info["empty"] > 0:
        if not step2_clean_data():
            return False
    
    # 步骤3: 重建索引
    rag_service = step3_rebuild_index()
    if not rag_service:
        return False
    
    # 步骤4: 测试查询
    if step4_test_query(rag_service):
        print("\n✅ 所有步骤完成，RAG查询功能已修复！")
        return True
    
    # 如果查询仍然失败，尝试完全重新加载
    print("\n⚠️  查询仍然失败，尝试完全重新加载...")
    rag_service = step5_full_reload()
    if not rag_service:
        return False
    
    # 再次测试查询
    if step4_test_query(rag_service):
        print("\n✅ 完全重新加载后，RAG查询功能已修复！")
        return True
    else:
        print("\n❌ 所有修复尝试都失败了")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
