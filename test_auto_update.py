#!/usr/bin/env python3
"""
测试自动更新功能
验证当CMS文章更新时间比RAG文档更新时，是否能自动更新
"""

import os
import sys
import time
from pathlib import Path
import requests
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from backend.app.rag_service import RAGService


def test_auto_update_functionality():
    """测试自动更新功能"""
    print("🧪 开始测试自动更新功能...")
    
    try:
        # 初始化 RAG 服务
        rag_service = RAGService()
        print("✅ RAG 服务初始化成功")
        
        # 测试数据
        test_content_v1 = "这是第一个版本的测试文档。"
        test_content_v2 = "这是第二个版本的测试文档，内容已经更新。"
        test_filename = "test_auto_update.txt"
        
        print(f"📝 测试文件名: {test_filename}")
        
        # 第一步：上传第一个版本
        print("\n📤 第一步：上传第一个版本...")
        result1 = rag_service.upload_document(
            file_content=test_content_v1,
            filename=test_filename,
            redirect_url="https://example.com/v1",
            content_url="https://example.com/content/v1"
        )
        
        if not result1["success"]:
            print(f"❌ 上传第一个版本失败: {result1['message']}")
            return False
        
        print(f"✅ 第一个版本上传成功: {result1['new_chunks']} 个文档块")
        
        # 获取第一个版本的元数据
        collection = rag_service.collection
        result = collection.get(
            where={"filename": test_filename},
            include=["metadatas"]
        )
        
        if not result["ids"]:
            print("❌ 未找到第一个版本的文档")
            return False
        
        # 记录第一个版本的时间戳
        first_version_time = result["metadatas"][0].get("file_modified", "")
        print(f"📅 第一个版本时间戳: {first_version_time}")
        
        # 等待一秒，确保时间戳不同
        time.sleep(1)
        
        # 第二步：模拟CMS更新，上传第二个版本
        print("\n📤 第二步：上传第二个版本（模拟CMS更新）...")
        result2 = rag_service.upload_document(
            file_content=test_content_v2,
            filename=test_filename,
            redirect_url="https://example.com/v2",
            content_url="https://example.com/content/v2"
        )
        
        if not result2["success"]:
            print(f"❌ 上传第二个版本失败: {result2['message']}")
            return False
        
        print(f"✅ 第二个版本上传成功: {result2['new_chunks']} 个文档块")
        print(f"🔄 替换了 {result2['old_chunks']} 个旧文档块")
        
        # 验证更新结果
        print("\n🔍 验证更新结果...")
        result = collection.get(
            where={"filename": test_filename},
            include=["metadatas"]
        )
        
        if not result["ids"]:
            print("❌ 未找到更新后的文档")
            return False
        
        # 检查文档块数量
        if len(result["ids"]) != result2["new_chunks"]:
            print(f"❌ 文档块数量不匹配: 期望 {result2['new_chunks']}, 实际 {len(result['ids'])}")
            return False
        
        print(f"✅ 文档块数量正确: {len(result['ids'])} 个")
        
        # 检查元数据更新
        metadata = result["metadatas"][0] if result["metadatas"] else {}
        second_version_time = metadata.get("file_modified", "")
        
        print(f"📅 第二个版本时间戳: {second_version_time}")
        
        # 验证时间戳已更新
        if second_version_time <= first_version_time:
            print("❌ 时间戳未更新")
            return False
        
        print("✅ 时间戳已正确更新")
        
        # 验证URL已更新
        redirect_url = metadata.get("redirect_url", "")
        content_url = metadata.get("content_url", "")
        
        if redirect_url != "https://example.com/v2":
            print(f"❌ redirect_url 未更新: {redirect_url}")
            return False
        
        if content_url != "https://example.com/content/v2":
            print(f"❌ content_url 未更新: {content_url}")
            return False
        
        print("✅ URL 已正确更新")
        
        # 验证内容已更新（通过查询验证）
        print("\n🔍 验证内容已更新...")
        query_result = rag_service.query("这是第二个版本的测试文档", max_results=1)
        
        if not query_result["success"]:
            print(f"❌ 查询失败: {query_result['message']}")
            return False
        
        if "第二个版本" not in query_result["answer"]:
            print("❌ 内容未正确更新")
            print(f"查询结果: {query_result['answer']}")
            return False
        
        print("✅ 内容已正确更新")
        
        # 清理测试文档
        print("\n🧹 清理测试文档...")
        delete_result = rag_service.delete_document(test_filename)
        if delete_result["success"]:
            print("✅ 测试文档清理成功")
        else:
            print(f"⚠️ 测试文档清理失败: {delete_result['message']}")
        
        print("\n🎉 自动更新功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_pipeline_integration():
    """测试数据管道集成"""
    print("\n🧪 测试数据管道集成...")
    
    try:
        # 测试数据管道服务的 /articles/to-add 接口
        response = requests.get("http://localhost:8001/articles/to-add", timeout=10)
        
        if response.status_code == 200:
            articles = response.json()
            print(f"✅ 数据管道接口正常，返回 {len(articles)} 篇文章")
            
            # 分析文章类型
            add_count = sum(1 for article in articles if article.get("action") == "add")
            update_count = sum(1 for article in articles if article.get("action") == "update")
            
            print(f"📊 文章分析: 新增 {add_count} 个, 更新 {update_count} 个")
            
            # 显示更新文章的详细信息
            update_articles = [article for article in articles if article.get("action") == "update"]
            if update_articles:
                print("\n🔄 需要更新的文章:")
                for article in update_articles[:3]:  # 只显示前3个
                    print(f"  • {article.get('title', 'N/A')} (ID: {article.get('content_id', 'N/A')})")
                    print(f"    更新时间: {article.get('update_time', 'N/A')}")
                    print(f"    redirect_url: {article.get('redirect_url', 'N/A')}")
                    print(f"    content_url: {article.get('content_url', 'N/A')}")
            
            return True
        else:
            print(f"❌ 数据管道接口异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据管道集成测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试自动更新功能")
    print("=" * 60)
    
    success = True
    
    # 测试基本自动更新功能
    if not test_auto_update_functionality():
        success = False
    
    # 测试数据管道集成
    if not test_data_pipeline_integration():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！自动更新功能正常工作")
    else:
        print("❌ 部分测试失败，请检查实现")
    
    sys.exit(0 if success else 1) 