#!/usr/bin/env python3
"""
测试 API 查询功能
"""

import requests
import json
import time

def test_api_status():
    """测试 API 状态"""
    print("🔍 测试 API 状态...")
    
    try:
        response = requests.get("http://localhost:8000/api/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API 状态正常")
            print(f"📊 文档数量: {data.get('documents_count', 'N/A')}")
            print(f"📊 存储大小: {data.get('storage_size', 'N/A')}")
            return True
        else:
            print(f"❌ API 状态异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API 连接失败: {e}")
        return False

def test_api_query():
    """测试 API 查询"""
    print("\n🔍 测试 API 查询...")
    
    query_data = {
        "query": "介绍一下新闻学",
        "max_results": 5,
        "similarity_threshold": 0.7
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json=query_data,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 查询成功")
            print(f"📊 答案长度: {len(data.get('answer', ''))}")
            print(f"📊 源文档数量: {len(data.get('sources', []))}")
            print(f"📄 答案预览: {data.get('answer', '')[:200]}...")
            
            if data.get('sources'):
                print(f"📋 源文档:")
                for i, source in enumerate(data['sources'][:2]):
                    print(f"   {i+1}. {source.get('filename', 'N/A')}")
            
            return True
        else:
            print(f"❌ 查询失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试 API 查询功能")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    # 测试 API 状态
    if not test_api_status():
        print("❌ API 状态测试失败")
        return False
    
    # 测试 API 查询
    if not test_api_query():
        print("❌ API 查询测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 所有 API 测试通过！")
    print("🎉 RAG 聊天功能正常工作")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
