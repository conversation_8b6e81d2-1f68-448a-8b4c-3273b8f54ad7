#!/usr/bin/env python3
"""
测试 redirect_url 和 content_url 写入功能
验证这些字段是否正确写入到 embedding_metadata 表中
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from backend.app.rag_service import RAGService


def test_redirect_content_url():
    """测试 redirect_url 和 content_url 写入功能"""
    print("🧪 开始测试 redirect_url 和 content_url 写入功能...")
    
    try:
        # 初始化 RAG 服务
        rag_service = RAGService()
        print("✅ RAG 服务初始化成功")
        
        # 测试数据
        test_content = "这是一个测试文档，用于验证 redirect_url 和 content_url 是否正确写入。"
        test_filename = "test_redirect_content.txt"
        test_redirect_url = "https://mp.weixin.qq.com/s/YV-73dyE_suj1C6vjInzTA"
        test_content_url = "https://gzmdrw.cn/tupianxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/680652086091845.shtml"
        
        print(f"📝 测试文件名: {test_filename}")
        print(f"🔗 redirect_url: {test_redirect_url}")
        print(f"🔗 content_url: {test_content_url}")
        
        # 上传文档
        result = rag_service.upload_document(
            file_content=test_content,
            filename=test_filename,
            redirect_url=test_redirect_url,
            content_url=test_content_url
        )
        
        if not result["success"]:
            print(f"❌ 上传文档失败: {result['message']}")
            return False
        
        print(f"✅ 文档上传成功: {result['new_chunks']} 个文档块")
        
        # 验证元数据是否正确写入
        print("\n🔍 验证元数据写入...")
        
        # 获取文档的元数据
        collection = rag_service.collection
        if not collection:
            print("❌ 集合未初始化")
            return False
        
        # 查询文档的元数据
        result = collection.get(
            where={"filename": test_filename},
            include=["metadatas"]
        )
        
        if not result["ids"]:
            print("❌ 未找到上传的文档")
            return False
        
        print(f"📊 找到 {len(result['ids'])} 个文档块")
        
        # 检查每个文档块的元数据
        for i, doc_id in enumerate(result["ids"]):
            metadata = result["metadatas"][i] if result["metadatas"] else {}
            print(f"\n📄 文档块 {i+1} (ID: {doc_id}):")
            print(f"  • filename: {metadata.get('filename', 'N/A')}")
            print(f"  • redirect_url: {metadata.get('redirect_url', 'N/A')}")
            print(f"  • content_url: {metadata.get('content_url', 'N/A')}")
            
            # 验证 URL 是否正确写入
            if metadata.get('redirect_url') == test_redirect_url:
                print("  ✅ redirect_url 写入正确")
            else:
                print(f"  ❌ redirect_url 写入错误: 期望 {test_redirect_url}, 实际 {metadata.get('redirect_url')}")
                return False
            
            if metadata.get('content_url') == test_content_url:
                print("  ✅ content_url 写入正确")
            else:
                print(f"  ❌ content_url 写入错误: 期望 {test_content_url}, 实际 {metadata.get('content_url')}")
                return False
        
        # 清理测试文档
        print("\n🧹 清理测试文档...")
        delete_result = rag_service.delete_document(test_filename)
        if delete_result["success"]:
            print("✅ 测试文档清理成功")
        else:
            print(f"⚠️ 测试文档清理失败: {delete_result['message']}")
        
        print("\n🎉 所有测试通过！redirect_url 和 content_url 功能正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_null_urls():
    """测试 null URL 的处理"""
    print("\n🧪 测试 null URL 处理...")
    
    try:
        rag_service = RAGService()
        
        test_content = "这是一个测试文档，用于验证 null URL 的处理。"
        test_filename = "test_null_urls.txt"
        
        # 上传文档，不传递 URL 参数
        result = rag_service.upload_document(
            file_content=test_content,
            filename=test_filename
        )
        
        if not result["success"]:
            print(f"❌ 上传文档失败: {result['message']}")
            return False
        
        print(f"✅ 文档上传成功: {result['new_chunks']} 个文档块")
        
        # 验证元数据
        collection = rag_service.collection
        result = collection.get(
            where={"filename": test_filename},
            include=["metadatas"]
        )
        
        if not result["ids"]:
            print("❌ 未找到上传的文档")
            return False
        
        # 检查元数据
        for i, doc_id in enumerate(result["ids"]):
            metadata = result["metadatas"][i] if result["metadatas"] else {}
            print(f"\n📄 文档块 {i+1}:")
            print(f"  • redirect_url: {metadata.get('redirect_url', 'N/A')}")
            print(f"  • content_url: {metadata.get('content_url', 'N/A')}")
            
            # 验证 null 值处理
            if metadata.get('redirect_url') is None or metadata.get('redirect_url') == '':
                print("  ✅ redirect_url 正确处理为 null/空")
            else:
                print(f"  ❌ redirect_url 应该为 null，实际为: {metadata.get('redirect_url')}")
                return False
            
            if metadata.get('content_url') is None or metadata.get('content_url') == '':
                print("  ✅ content_url 正确处理为 null/空")
            else:
                print(f"  ❌ content_url 应该为 null，实际为: {metadata.get('content_url')}")
                return False
        
        # 清理
        rag_service.delete_document(test_filename)
        print("✅ null URL 测试通过")
        return True
        
    except Exception as e:
        print(f"❌ null URL 测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试 redirect_url 和 content_url 功能")
    print("=" * 60)
    
    success = True
    
    # 测试正常 URL 写入
    if not test_redirect_content_url():
        success = False
    
    # 测试 null URL 处理
    if not test_null_urls():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！redirect_url 和 content_url 功能实现正确")
    else:
        print("❌ 部分测试失败，请检查实现")
    
    sys.exit(0 if success else 1) 