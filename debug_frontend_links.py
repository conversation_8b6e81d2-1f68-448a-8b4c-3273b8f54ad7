#!/usr/bin/env python3
"""
调试前端链接显示问题
"""

import requests
import json

def test_api_response():
    """测试API响应格式"""
    print("🔍 测试API响应格式...")
    
    try:
        query_data = {
            "query": "学校有哪些专业",
            "max_results": 3,
            "similarity_threshold": 0.7
        }
        
        response = requests.post(
            "http://localhost:8000/api/query",
            json=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API响应成功")
            print(f"📊 源文档数量: {len(data.get('sources', []))}")
            
            print(f"\n📋 详细的API响应格式:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查每个源文档的字段
            print(f"\n🔍 检查源文档字段:")
            for i, source in enumerate(data.get('sources', []), 1):
                print(f"\n   源文档 {i}:")
                for key, value in source.items():
                    print(f"     {key}: {value}")
            
            return True
        else:
            print(f"❌ API响应失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_test_html():
    """生成测试HTML来验证前端代码"""
    print(f"\n🔧 生成测试HTML...")
    
    # 模拟API响应数据
    test_data = {
        "sources": [
            {
                "filename": "测试文档1.txt",
                "content": "这是一个测试文档的内容，用于验证链接显示功能是否正常工作。",
                "score": 0.85,
                "url": "https://example.com/test1",
                "url_type": "content"
            },
            {
                "filename": "测试文档2.txt", 
                "content": "这是另一个测试文档，这个文档有redirect_url。",
                "score": 0.75,
                "url": "https://weixin.qq.com/test2",
                "url_type": "redirect"
            },
            {
                "filename": "测试文档3.txt",
                "content": "这个文档没有链接信息。",
                "score": 0.65
            }
        ]
    }
    
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接显示测试</title>
    <link rel="stylesheet" href="frontend/static/css/style.css">
</head>
<body>
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
        <h2>链接显示测试</h2>
        <div id="test-sources"></div>
        
        <script>
            // 模拟ChatApp的createSourcesDiv方法
            function createSourcesDiv(sources) {{
                const sourcesDiv = document.createElement("div");
                sourcesDiv.className = "message-sources";

                const titleDiv = document.createElement("h4");
                titleDiv.textContent = "参考来源：";
                sourcesDiv.appendChild(titleDiv);

                sources.forEach((source) => {{
                    const sourceDiv = document.createElement("div");
                    sourceDiv.className = "source-item";

                    // 构建链接HTML
                    let linkHtml = '';
                    if (source.url) {{
                        const linkIcon = source.url_type === 'redirect' ? '🔗' : '📖';
                        const linkText = source.url_type === 'redirect' ? '查看原文' : '访问链接';
                        linkHtml = `
                          <div class="source-link">
                            <a href="${{source.url}}" target="_blank" rel="noopener noreferrer">
                              ${{linkIcon}} ${{linkText}}
                            </a>
                          </div>
                        `;
                    }}

                    sourceDiv.innerHTML = `
                        <div class="source-filename">
                            📄 ${{source.filename}}
                            <span class="source-score">相似度: ${{(source.score * 100).toFixed(1)}}%</span>
                        </div>
                        <div class="source-content">${{source.content}}</div>
                        ${{linkHtml}}
                    `;

                    sourcesDiv.appendChild(sourceDiv);
                }});

                return sourcesDiv;
            }}
            
            // 测试数据
            const testSources = {json.dumps(test_data['sources'], ensure_ascii=False)};
            
            // 创建并显示测试结果
            document.addEventListener('DOMContentLoaded', function() {{
                const container = document.getElementById('test-sources');
                const sourcesDiv = createSourcesDiv(testSources);
                container.appendChild(sourcesDiv);
            }});
        </script>
    </div>
</body>
</html>
"""
    
    with open("test_links.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"✅ 测试HTML已生成: test_links.html")
    print(f"💡 请在浏览器中打开此文件来测试链接显示")

def main():
    """主函数"""
    print("🚀 调试前端链接显示问题")
    print("=" * 60)
    
    # 测试API响应
    if test_api_response():
        print("\n✅ API响应正常，包含链接信息")
    else:
        print("\n❌ API响应异常")
        return False
    
    # 生成测试HTML
    generate_test_html()
    
    print("\n" + "=" * 60)
    print("🔍 可能的问题:")
    print("1. 浏览器缓存了旧版本的JavaScript文件")
    print("2. 需要强制刷新页面 (Ctrl+F5 或 Cmd+Shift+R)")
    print("3. 检查浏览器开发者工具的Console是否有JavaScript错误")
    print("4. 确认前端服务器已重启并加载了新代码")
    
    return True

if __name__ == "__main__":
    main()
