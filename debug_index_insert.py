#!/usr/bin/env python3
"""
调试索引插入问题
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService
from llama_index.core import Document

def test_simple_insert():
    """测试简单的文档插入"""
    print("🔍 测试简单的文档插入...")
    
    try:
        rag_service = RAGService()
        
        # 检查初始状态
        print(f"📊 初始 ChromaDB 文档数: {rag_service.collection.count()}")
        print(f"📊 初始 docstore 文档数: {len(rag_service.index.docstore.docs)}")
        
        # 创建一个简单的测试文档
        test_doc = Document(
            text="这是一个测试文档，用于验证索引插入功能。",
            metadata={
                "filename": "test_insert.txt",
                "test": True
            }
        )
        
        print(f"📝 测试文档内容: {test_doc.text}")
        print(f"📝 测试文档元数据: {test_doc.metadata}")
        
        # 插入文档
        print("📥 插入文档到索引...")
        rag_service.index.insert(test_doc)
        
        # 检查插入后的状态
        print(f"📊 插入后 ChromaDB 文档数: {rag_service.collection.count()}")
        print(f"📊 插入后 docstore 文档数: {len(rag_service.index.docstore.docs)}")
        
        # 检查 docstore 中的文档
        if rag_service.index.docstore.docs:
            print("📋 docstore 中的文档:")
            for doc_id, doc in list(rag_service.index.docstore.docs.items())[:3]:
                print(f"   ID: {doc_id}")
                print(f"   文本: {doc.text[:50]}...")
                print(f"   元数据: {doc.metadata}")
        
        # 测试查询
        print("\n🔍 测试查询...")
        rag_service._create_query_engine()
        result = rag_service.query("测试文档", max_results=1)
        
        if result["success"]:
            print(f"✅ 查询成功: {result['answer'][:100]}...")
        else:
            print(f"❌ 查询失败: {result['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_processing():
    """测试文件处理"""
    print("\n🔍 测试文件处理...")
    
    try:
        rag_service = RAGService()
        
        # 找一个测试文件
        data_path = Path("data")
        if not data_path.exists():
            print("❌ data 目录不存在")
            return False
        
        txt_files = list(data_path.glob("*.txt"))
        if not txt_files:
            print("❌ 没有找到 txt 文件")
            return False
        
        test_file = txt_files[0]
        print(f"📁 测试文件: {test_file}")
        
        # 检查初始状态
        initial_chroma_count = rag_service.collection.count()
        initial_docstore_count = len(rag_service.index.docstore.docs)
        
        print(f"📊 处理前 ChromaDB 文档数: {initial_chroma_count}")
        print(f"📊 处理前 docstore 文档数: {initial_docstore_count}")
        
        # 处理文件
        print("📥 处理文件...")
        rag_service._process_single_file(test_file)
        
        # 检查处理后的状态
        final_chroma_count = rag_service.collection.count()
        final_docstore_count = len(rag_service.index.docstore.docs)
        
        print(f"📊 处理后 ChromaDB 文档数: {final_chroma_count}")
        print(f"📊 处理后 docstore 文档数: {final_docstore_count}")
        
        print(f"📊 ChromaDB 增加: {final_chroma_count - initial_chroma_count}")
        print(f"📊 docstore 增加: {final_docstore_count - initial_docstore_count}")
        
        # 检查是否同步
        if (final_chroma_count - initial_chroma_count) == (final_docstore_count - initial_docstore_count):
            print("✅ ChromaDB 和 docstore 同步")
        else:
            print("❌ ChromaDB 和 docstore 不同步")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_llamaindex_version():
    """检查 LlamaIndex 版本"""
    print("🔍 检查 LlamaIndex 版本...")
    
    try:
        import llama_index
        print(f"📦 llama_index 版本: {llama_index.__version__}")
        
        from llama_index.core import __version__ as core_version
        print(f"📦 llama_index.core 版本: {core_version}")
        
    except Exception as e:
        print(f"❌ 检查版本失败: {e}")

def main():
    """主函数"""
    print("🚀 开始调试索引插入问题")
    print("=" * 60)
    
    # 检查版本
    check_llamaindex_version()
    
    print("\n" + "=" * 60)
    
    # 测试简单插入
    if not test_simple_insert():
        print("❌ 简单插入测试失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试文件处理
    if not test_file_processing():
        print("❌ 文件处理测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 调试完成")

if __name__ == "__main__":
    main()
