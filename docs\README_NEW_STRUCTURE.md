# 🏗️ 项目结构说明

## 📁 目录结构

```
fast-gzmdrw-chat/
├── backend/
│   ├── app/
│   │   ├── main.py                 # 主API服务 (端口8000)
│   │   ├── data_pipeline.py        # 数据管道API服务 (端口8001)
│   │   ├── rag_service.py          # RAG服务
│   │   └── data_pipeline/          # 数据管道相关文件
│   │       ├── start_apis.py       # 启动脚本
│   │       ├── test_apis.py        # 测试脚本
│   │       ├── example_usage.py    # 使用示例
│   │       ├── start_apis.bat      # Windows启动脚本
│   │       ├── test_apis.bat       # Windows测试脚本
│   │       ├── API_SERVICES_README.md # API文档
│   │       ├── QUICK_START.md      # 快速启动指南
│   │       └── data_pipeline_config_example.env # 配置示例
│   └── config/
│       └── settings.py             # 配置文件
├── frontend/                       # 前端文件
├── data/                           # 数据目录
├── storage/                        # 存储目录
├── .env                           # 环境变量文件
├── requirements.txt               # Python依赖
└── README.md                      # 项目主文档
```

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 复制配置文件示例
cp backend/app/data_pipeline/data_pipeline_config_example.env .env

# 编辑 .env 文件，添加数据库配置
```

### 3. 启动服务
```bash
# 方法1: 使用Python脚本
python backend/app/data_pipeline/start_apis.py

# 方法2: 使用Windows批处理文件
backend\app\data_pipeline\start_apis.bat
```

### 4. 测试服务
```bash
# 方法1: 使用Python脚本
python backend/app/data_pipeline/test_apis.py

# 方法2: 使用Windows批处理文件
backend\app\data_pipeline\test_apis.bat
```

## 🔗 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主API服务 | http://localhost:8000 | RAG聊天应用 |
| 主API文档 | http://localhost:8000/docs | Swagger文档 |
| 数据管道API服务 | http://localhost:8001 | ChestnutCMS数据处理 |
| 数据管道API文档 | http://localhost:8001/docs | Swagger文档 |

## 📚 文档

- **快速启动**: `backend/app/data_pipeline/QUICK_START.md`
- **API文档**: `backend/app/data_pipeline/API_SERVICES_README.md`
- **使用示例**: `backend/app/data_pipeline/example_usage.py`

## 🛠️ 开发

### 单独启动服务
```bash
# 启动主API服务
cd backend/app
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 启动数据管道API服务
cd backend/app
uvicorn data_pipeline:app --host 0.0.0.0 --port 8001 --reload
```

### 环境变量配置
在 `.env` 文件中配置以下变量：

```env
# OpenAI API配置
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://api.openai-proxy.org/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# 数据库配置
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name

# 数据管道服务配置
DATA_PIPELINE_HOST=0.0.0.0
DATA_PIPELINE_PORT=8001

# RAG服务配置
RAG_SERVICE_URL=http://localhost:8003

# 网站配置
SITE_BASE_URL=https://gzmdrw.cn
```

## 📞 支持

如有问题，请：
1. 查看服务日志
2. 运行测试脚本: `python backend/app/data_pipeline/test_apis.py`
3. 检查配置文件
4. 确认数据库连接
5. 查看详细文档: `backend/app/data_pipeline/API_SERVICES_README.md` 