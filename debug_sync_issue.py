#!/usr/bin/env python3
"""
调试同步问题：为什么总是显示"新增 2 个"
"""

import requests
import json

def debug_sync_status():
    """调试同步状态"""
    print("🔍 调试同步状态...")
    
    try:
        # 1. 获取 debug/sync-status 信息
        print("\n📊 获取详细同步状态...")
        response = requests.get("http://localhost:8001/debug/sync-status")
        if response.status_code == 200:
            sync_data = response.json()
            print(f"✅ CMS 文章数量: {sync_data['cms_count']}")
            print(f"✅ RAG 文档数量: {sync_data['rag_count']}")
            print(f"✅ CMS中有但RAG中没有的文章数: {len(sync_data['cms_titles_not_in_rag'])}")
            
            if sync_data['cms_titles_not_in_rag']:
                print(f"\n📋 CMS中有但RAG中没有的文章:")
                for i, title in enumerate(sync_data['cms_titles_not_in_rag'][:10]):  # 只显示前10个
                    print(f"   {i+1}. {title}")
                if len(sync_data['cms_titles_not_in_rag']) > 10:
                    print(f"   ... 还有 {len(sync_data['cms_titles_not_in_rag']) - 10} 个")
        else:
            print(f"❌ 获取同步状态失败: {response.status_code}")
            return False
        
        # 2. 获取 to-add 接口信息
        print(f"\n📊 获取 to-add 接口信息...")
        response = requests.get("http://localhost:8001/articles/to-add")
        if response.status_code == 200:
            to_add_data = response.json()
            print(f"✅ to-add 接口返回文章数: {len(to_add_data)}")
            
            # 统计操作类型
            add_count = sum(1 for article in to_add_data if article.get('action') == 'add')
            update_count = sum(1 for article in to_add_data if article.get('action') == 'update')
            
            print(f"📊 其中新增: {add_count} 个")
            print(f"📊 其中更新: {update_count} 个")
            
            if to_add_data:
                print(f"\n📋 to-add 接口返回的文章:")
                for i, article in enumerate(to_add_data[:10]):  # 只显示前10个
                    action = article.get('action', 'unknown')
                    print(f"   {i+1}. {article.get('title', 'N/A')} (操作: {action})")
                    print(f"      文件名: {article.get('filename', 'N/A')}")
                    print(f"      content_id: {article.get('content_id', 'N/A')}")
                if len(to_add_data) > 10:
                    print(f"   ... 还有 {len(to_add_data) - 10} 个")
        else:
            print(f"❌ 获取 to-add 信息失败: {response.status_code}")
            return False
        
        # 3. 获取 RAG 文档列表
        print(f"\n📊 获取 RAG 文档列表...")
        response = requests.get("http://localhost:8000/api/documents")
        if response.status_code == 200:
            rag_data = response.json()
            print(f"✅ RAG 文档数量: {len(rag_data.get('documents', []))}")
            
            rag_filenames = {doc['filename'] for doc in rag_data.get('documents', [])}
            print(f"📋 RAG 文档文件名 (前10个):")
            for i, filename in enumerate(sorted(list(rag_filenames))[:10]):
                print(f"   {i+1}. {filename}")
        else:
            print(f"❌ 获取 RAG 文档列表失败: {response.status_code}")
            return False
        
        # 4. 分析差异
        print(f"\n🔍 分析差异...")
        cms_not_in_rag = set(sync_data['cms_titles_not_in_rag'])
        to_add_filenames = {article.get('filename') for article in to_add_data if article.get('action') == 'add'}
        
        print(f"📊 debug/sync-status 显示需要新增: {len(cms_not_in_rag)} 个")
        print(f"📊 to-add 接口显示需要新增: {len(to_add_filenames)} 个")
        
        if cms_not_in_rag != to_add_filenames:
            print(f"⚠️  两个接口的结果不一致!")
            print(f"   debug/sync-status 独有: {cms_not_in_rag - to_add_filenames}")
            print(f"   to-add 独有: {to_add_filenames - cms_not_in_rag}")
        else:
            print(f"✅ 两个接口的结果一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def test_specific_articles():
    """测试具体的文章获取"""
    print(f"\n🔍 测试具体文章的内容获取...")
    
    # 从日志中看到的失败的文章ID
    failed_ids = [
        "310000000002751",
        "685650404978757", 
        "685650870968389",
        "685650916724805",
        "685650973442117"
    ]
    
    for article_id in failed_ids:
        print(f"\n📝 测试文章 ID: {article_id}")
        try:
            response = requests.get(f"http://localhost:8001/articles/{article_id}/txt")
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                content = response.text
                print(f"   内容长度: {len(content)} 字符")
                print(f"   内容预览: {content[:100]}...")
            else:
                print(f"   错误: {response.text}")
        except Exception as e:
            print(f"   异常: {e}")

def main():
    """主函数"""
    print("🚀 开始调试同步问题")
    print("=" * 60)
    
    # 调试同步状态
    if not debug_sync_status():
        print("❌ 同步状态调试失败")
        return False
    
    # 测试具体文章
    test_specific_articles()
    
    print("\n" + "=" * 60)
    print("✅ 调试完成")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
