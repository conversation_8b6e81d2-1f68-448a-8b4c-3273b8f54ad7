# RAG应用API服务说明

本项目包含两个独立的FastAPI服务，分别运行在不同的端口上，使用独立的配置文件：

## 🚀 服务概览

### 1. 主API服务 (端口8000)
- **文件**: `backend/app/main.py`
- **端口**: 8000
- **配置文件**: `backend/app/.env`
- **功能**: RAG聊天应用的核心服务，提供文档管理、问答查询等功能

### 2. 数据管道API服务 (端口8001)
- **文件**: `backend/app/data_pipeline.py`
- **端口**: 8001
- **配置文件**: `backend/app/data_pipeline.env`
- **功能**: ChestnutCMS数据处理服务，提供文章管理、数据同步等功能

## 📋 快速开始

### 配置文件设置

#### 1. 主API服务配置
```bash
# 配置文件位置: backend/app/.env
# 包含OpenAI API配置和应用配置
```

#### 2. 数据管道服务配置
```bash
# 复制配置示例
cp backend/app/data_pipeline_config_example.env backend/app/data_pipeline.env

# 编辑配置文件，设置数据库连接信息
# 配置文件位置: backend/app/data_pipeline.env
```

### 启动所有服务

使用启动脚本同时启动两个服务：

```bash
# 从项目根目录运行
python backend/app/data_pipeline/start_apis.py

# 或使用Windows批处理文件
start_services.bat
```

### 单独启动服务

#### 启动主API服务
```bash
cd backend/app
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

#### 启动数据管道API服务
```bash
cd backend/app
uvicorn data_pipeline:app --host 0.0.0.0 --port 8001 --reload
```

### 测试服务

运行测试脚本验证服务状态：

```bash
# 从项目根目录运行
python backend/app/data_pipeline/test_apis.py

# 或使用Windows批处理文件
test_services.bat
```

## 🔗 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主API服务 | http://localhost:8000 | RAG聊天应用主服务 |
| 主API文档 | http://localhost:8000/docs | Swagger API文档 |
| 数据管道API服务 | http://localhost:8001 | ChestnutCMS数据处理服务 |
| 数据管道API文档 | http://localhost:8001/docs | Swagger API文档 |

## 📚 API接口说明

### 主API服务接口 (端口8000)

#### 基础接口
- `GET /` - 服务健康检查
- `GET /api/status` - 获取系统状态
- `GET /` - 聊天页面
- `GET /documents` - 文档管理页面

#### 文档管理
- `GET /api/documents` - 获取文档列表
- `POST /api/documents/upload` - 上传文档
- `DELETE /api/documents/{filename}` - 删除文档
- `POST /api/load-documents` - 重新加载文档

#### 问答查询
- `POST /api/query` - 查询问答

### 数据管道API服务接口 (端口8001)

#### 基础接口
- `GET /` - 服务健康检查
- `GET /debug/rag-connection` - 测试与RAG服务的连接状态

#### 文章管理
- `GET /articles/summary` - 获取文章摘要统计
- `GET /articles/to-add` - 获取需要添加到文档管理的文章
- `GET /articles/to-delete` - 获取需要从文档管理删除的文章
- `GET /articles/to-update` - 获取需要更新的文章
- `GET /articles/{article_id}/txt` - 获取指定文章的纯文本内容
- `GET /articles/` - 下载所有文章

## 💡 使用示例

### 1. 测试RAG服务连接

```python
import requests

# 测试与RAG服务的连接
response = requests.get("http://localhost:8001/debug/rag-connection")
debug_info = response.json()

print(f"RAG服务URL: {debug_info['rag_service_url']}")
print(f"健康检查: {debug_info['connection_test']['health_check']['status']}")
print(f"状态API: {debug_info['connection_test']['status_api']['status']}")
print(f"文档API: {debug_info['connection_test']['documents_api']['status']}")
print(f"文档数量: {debug_info['connection_test']['documents_api']['documents_count']}")
```

### 2. 获取文章摘要示例

```python
import requests

# 获取文章摘要
response = requests.get("http://localhost:8001/articles/summary")
summary = response.json()

print(f"总文章数: {summary['total_articles']}")
print(f"需要添加: {summary['to_add']}")
print(f"需要删除: {summary['to_delete']}")
print(f"需要更新: {summary['to_update']}")
```

### 3. 获取需要添加的文章示例

```python
import requests

# 获取需要添加的文章
response = requests.get("http://localhost:8001/articles/to-add")
articles = response.json()

for article in articles:
    print(f"文章ID: {article['content_id']}")
    print(f"标题: {article['title']}")
    print(f"内容类型: {article['content_type']}")
    print("---")
```

### 4. 获取文章纯文本内容示例

```python
import requests

# 获取指定文章的纯文本内容
article_id = 123
response = requests.get(f"http://localhost:8001/articles/{article_id}/txt")

if response.status_code == 200:
    content = response.text
    print(f"文章 {article_id} 的纯文本内容:")
    print(content)
else:
    print(f"获取失败: {response.status_code}")
```

## 🔧 配置说明

### 主API服务配置 (`backend/app/.env`)
```env
# OpenAI API配置
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://api.openai-proxy.org/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# ChromaDB配置
CHROMA_PERSIST_DIRECTORY=./storage

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
```

### 数据管道API服务配置 (`backend/app/data_pipeline.env`)
```env
# 数据管道API服务配置
DATA_PIPELINE_HOST=0.0.0.0
DATA_PIPELINE_PORT=8001

# 数据库配置 (ChestnutCMS)
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name

# 网站配置
SITE_BASE_URL=https://gzmdrw.cn

# RAG服务配置 - 指向本项目的端口8000
RAG_SERVICE_URL=http://localhost:8000

# 数据处理配置
MAX_CONTENT_LENGTH=10485760
SUPPORTED_FILE_TYPES=.txt,.html,.htm
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 安全配置
CORS_ORIGINS=["*"]
RATE_LIMIT_PER_MINUTE=100
```

## 📁 目录结构

```
fast-gzmdrw-chat/
├── backend/
│   ├── app/
│   │   ├── .env                        # 主API服务配置
│   │   ├── data_pipeline.env           # 数据管道服务配置
│   │   ├── data_pipeline_config_example.env # 配置示例
│   │   ├── main.py                     # 主API服务
│   │   ├── data_pipeline.py            # 数据管道API服务
│   │   ├── rag_service.py              # RAG服务
│   │   └── data_pipeline/              # 数据管道相关文件
│   │       ├── start_apis.py           # 启动脚本
│   │       ├── test_apis.py            # 测试脚本
│   │       ├── example_usage.py        # 使用示例
│   │       ├── start_apis.bat          # Windows启动脚本
│   │       ├── test_apis.bat           # Windows测试脚本
│   │       ├── API_SERVICES_README.md  # API文档
│   │       ├── QUICK_START.md          # 快速启动指南
│   │       └── data_pipeline_config_example.env # 配置示例
│   └── config/
│       └── settings.py                 # 配置文件
├── data/                               # 数据目录
├── storage/                            # 存储目录
├── .env                               # 旧配置文件（已迁移）
├── requirements.txt                   # Python依赖
└── README.md                          # 项目主文档
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :8000
   netstat -ano | findstr :8001
   
   # 杀死占用进程
   taskkill /PID <进程ID> /F
   ```

2. **服务启动失败**
   - 检查Python环境和依赖
   - 确保配置文件正确
   - 检查数据库连接
   - 查看日志输出

3. **API调用失败**
   - 确认服务已启动
   - 检查请求格式
   - 查看服务日志
   - 检查数据库连接

### 数据库配置

数据管道API服务需要MySQL数据库连接，请确保在 `backend/app/data_pipeline.env` 文件中配置以下变量：

```env
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name
```

### 日志查看

服务启动时会显示详细的日志信息，包括：
- 服务启动状态
- 数据库连接状态
- API请求日志
- 错误信息

## 📞 支持

如有问题，请：
1. 查看服务日志
2. 运行测试脚本
3. 检查配置文件
4. 确认数据库连接
5. 检查网络连接

---

**注意**: 在生产环境中，请确保：
- 配置适当的安全设置
- 限制CORS访问
- 使用HTTPS
- 设置适当的日志级别
- 保护数据库连接信息 

## 同步链路与调试

### 分工说明
- 数据管道（data_pipeline.py）负责CMS与RAG服务之间的同步、比对、调度。
- RAG服务（main.py）负责本地data目录与向量数据库的文档管理。

### 同步流程
1. 前端调用 `/articles/to-add` 获取所有待同步文章（不再限制正文内容）。
2. 依次请求 `/articles/{article_id}/txt` 获取txt内容。
3. 拿到内容后，调用 `/api/documents/upload` 上传。
4. 上传成功后，data目录和RAG数据库同步更新。

### 常见异常与调试建议
- RAG数据库有文档但data目录无文件：多为RAG服务写文件异常被吞掉，需检查rag_service.py的文件写入逻辑和DATA_DIR配置。
- /articles/to-add返回的文章无法同步：多为正文和redirect_url都为空，/articles/{article_id}/txt会404。
- /debug/sync-status 可一键比对CMS与RAG的所有差异，定位缺失文件。
- 建议在RAG服务的文件写入处加详细日志，确保异常能反馈到前端。 