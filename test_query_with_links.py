#!/usr/bin/env python3
"""
测试查询结果是否包含链接信息
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.rag_service import RAGService

def test_direct_query():
    """直接测试RAG服务查询"""
    print("🔍 直接测试RAG服务查询...")
    
    try:
        rag_service = RAGService()
        
        # 测试查询
        test_queries = [
            "介绍一下新闻学",
            "学校有哪些专业",
            "党政办公室"
        ]
        
        for query in test_queries:
            print(f"\n📝 测试查询: {query}")
            result = rag_service.query(query, max_results=3)
            
            if result["success"]:
                print(f"✅ 查询成功")
                print(f"📊 源文档数量: {len(result['sources'])}")
                
                for i, source in enumerate(result['sources'], 1):
                    print(f"\n   📄 源文档 {i}:")
                    print(f"      文件名: {source['filename']}")
                    print(f"      相似度: {source.get('score', 0):.3f}")
                    print(f"      内容: {source['content'][:100]}...")
                    
                    # 检查链接信息
                    if 'url' in source:
                        print(f"      🔗 链接: {source['url']}")
                        print(f"      🏷️  链接类型: {source.get('url_type', 'unknown')}")
                    else:
                        print(f"      ❌ 无链接信息")
            else:
                print(f"❌ 查询失败: {result['message']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 直接查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_query():
    """测试API查询"""
    print("\n🔍 测试API查询...")
    
    try:
        query_data = {
            "query": "介绍一下新闻学",
            "max_results": 3,
            "similarity_threshold": 0.7
        }
        
        response = requests.post(
            "http://localhost:8000/api/query",
            json=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API查询成功")
            print(f"📊 源文档数量: {len(data.get('sources', []))}")
            
            for i, source in enumerate(data.get('sources', []), 1):
                print(f"\n   📄 源文档 {i}:")
                print(f"      文件名: {source.get('filename', 'N/A')}")
                print(f"      相似度: {source.get('score', 0):.3f}")
                print(f"      内容: {source.get('content', '')[:100]}...")
                
                # 检查链接信息
                if 'url' in source:
                    print(f"      🔗 链接: {source['url']}")
                    print(f"      🏷️  链接类型: {source.get('url_type', 'unknown')}")
                else:
                    print(f"      ❌ 无链接信息")
            
            return True
        else:
            print(f"❌ API查询失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API查询测试失败: {e}")
        return False

def check_document_metadata():
    """检查文档元数据中的链接信息"""
    print("\n🔍 检查文档元数据中的链接信息...")
    
    try:
        rag_service = RAGService()
        collection = rag_service.collection
        
        # 获取样本文档的元数据
        result = collection.get(include=["metadatas"], limit=10)
        
        print(f"📊 检查 {len(result['ids'])} 个文档的元数据")
        
        redirect_count = 0
        content_count = 0
        no_url_count = 0
        
        for i, metadata in enumerate(result["metadatas"]):
            if metadata:
                filename = metadata.get("filename", "unknown")
                redirect_url = metadata.get("redirect_url")
                content_url = metadata.get("content_url")
                
                if redirect_url:
                    redirect_count += 1
                    print(f"   📄 {filename}: redirect_url = {redirect_url}")
                elif content_url:
                    content_count += 1
                    print(f"   📄 {filename}: content_url = {content_url}")
                else:
                    no_url_count += 1
                    print(f"   📄 {filename}: 无链接信息")
        
        print(f"\n📊 统计:")
        print(f"   🔗 有 redirect_url: {redirect_count}")
        print(f"   📖 有 content_url: {content_count}")
        print(f"   ❌ 无链接信息: {no_url_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查元数据失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试查询结果中的链接信息")
    print("=" * 60)
    
    # 检查文档元数据
    if not check_document_metadata():
        print("❌ 元数据检查失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 直接测试RAG服务
    if not test_direct_query():
        print("❌ 直接查询测试失败")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试API查询
    if not test_api_query():
        print("❌ API查询测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！")
    print("🎉 查询结果现在包含链接信息")
    print("💡 用户可以通过参考来源中的链接访问原文")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
