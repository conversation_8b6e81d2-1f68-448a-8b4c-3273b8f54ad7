#!/usr/bin/env python3
"""
测试API查询是否包含链接信息
"""

import requests
import json
import time

def test_api_query_with_links():
    """测试API查询包含链接"""
    print("🔍 测试API查询包含链接...")
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    try:
        # 测试状态
        response = requests.get("http://localhost:8000/api/status", timeout=10)
        if response.status_code != 200:
            print(f"❌ 服务未启动: {response.status_code}")
            return False
        
        print("✅ 服务已启动")
        
        # 测试查询
        query_data = {
            "query": "介绍一下新闻学专业",
            "max_results": 3,
            "similarity_threshold": 0.7
        }
        
        print(f"📝 发送查询: {query_data['query']}")
        
        response = requests.post(
            "http://localhost:8000/api/query",
            json=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API查询成功")
            print(f"📊 答案长度: {len(data.get('answer', ''))}")
            print(f"📊 源文档数量: {len(data.get('sources', []))}")
            
            print(f"\n📋 参考来源:")
            for i, source in enumerate(data.get('sources', []), 1):
                print(f"\n   📄 源文档 {i}:")
                print(f"      文件名: {source.get('filename', 'N/A')}")
                print(f"      相似度: {source.get('score', 0):.3f}")
                print(f"      内容: {source.get('content', '')[:100]}...")
                
                # 检查链接信息
                if 'url' in source:
                    print(f"      🔗 链接: {source['url']}")
                    print(f"      🏷️  链接类型: {source.get('url_type', 'unknown')}")
                else:
                    print(f"      ❌ 无链接信息")
            
            # 检查是否有链接
            sources_with_links = [s for s in data.get('sources', []) if 'url' in s]
            print(f"\n📊 包含链接的源文档: {len(sources_with_links)}/{len(data.get('sources', []))}")
            
            return len(sources_with_links) > 0
        else:
            print(f"❌ API查询失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试API查询中的链接功能")
    print("=" * 60)
    
    if test_api_query_with_links():
        print("\n✅ 测试成功！")
        print("🎉 API查询结果现在包含链接信息")
        print("💡 前端可以显示文章的原文链接")
    else:
        print("\n❌ 测试失败")
    
    return True

if __name__ == "__main__":
    main()
